<?php
require_once 'config.php';

/**
 * Cloudflare API 请求封装函数
 *
 * @param string $method HTTP 方法 (GET, POST, PUT, DELETE)
 * @param string $endpoint API 端点
 * @param array|null $data 请求数据
 * @return array API 响应数据
 */
function cf_api_request($method, $endpoint, $data = null) {
    // 检查会话中是否有 token
    if (!isset($_SESSION['token'])) {
        return [
            'success' => false,
            'errors' => [['message' => get_error_message('invalid_token')]]
        ];
    }

    $headers = [
        "Authorization: Bearer " . $_SESSION['token'],
        "Content-Type: application/json",
        "User-Agent: " . get_config('APP_NAME', 'Cloudflare-DNS-UI') . "/" . get_config('APP_VERSION', '1.0')
    ];

    $url = get_config('CLOUDFLARE_API_BASE', 'https://api.cloudflare.com/client/v4/') . $endpoint;
    $ch = curl_init($url);

    // 基础 cURL 设置
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => get_config('API_TIMEOUT', 30),
        CURLOPT_CONNECTTIMEOUT => get_config('API_CONNECT_TIMEOUT', 10),
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2,
        CURLOPT_FOLLOWLOCATION => false
    ]);

    // 如果有数据，添加到请求体
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    // 记录 API 请求日志
    if (get_config('LOG_API_REQUESTS', false)) {
        debug_log("API Request: $method $url", ['data' => $data]);
    }

    // 检查 cURL 错误
    if ($curlError) {
        debug_log("cURL Error: $curlError");
        return [
            'success' => false,
            'errors' => [['message' => get_error_message('network_error')]]
        ];
    }

    // 检查 HTTP 状态码
    if ($httpCode >= 400) {
        $errorData = json_decode($response, true);
        debug_log("HTTP Error $httpCode", ['response' => $errorData]);

        return [
            'success' => false,
            'errors' => $errorData['errors'] ?? [['message' => get_error_message('api_error')]]
        ];
    }

    // 解析 JSON 响应
    $decodedResponse = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        debug_log("JSON Parse Error: " . json_last_error_msg());
        return [
            'success' => false,
            'errors' => [['message' => 'Invalid JSON response from API']]
        ];
    }

    // 记录成功响应
    if (get_config('LOG_API_REQUESTS', false)) {
        debug_log("API Response: Success", ['result_count' => count($decodedResponse['result'] ?? [])]);
    }

    return $decodedResponse;
}

/**
 * 验证 Cloudflare API Token 是否有效
 *
 * @param string $token API Token
 * @return array 验证结果
 */
function validate_cf_token($token) {
    $headers = [
        "Authorization: Bearer $token",
        "Content-Type: application/json",
        "User-Agent: " . get_config('APP_NAME', 'Cloudflare-DNS-UI') . "/" . get_config('APP_VERSION', '1.0')
    ];

    $url = get_config('CLOUDFLARE_API_BASE', 'https://api.cloudflare.com/client/v4/') . 'user/tokens/verify';
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => get_config('API_TIMEOUT', 30),
        CURLOPT_CONNECTTIMEOUT => get_config('API_CONNECT_TIMEOUT', 10),
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    debug_log("Token validation attempt", ['http_code' => $httpCode]);

    if ($curlError) {
        debug_log("Token validation cURL error: $curlError");
        return [
            'valid' => false,
            'error' => get_error_message('network_error')
        ];
    }

    if ($httpCode !== 200) {
        debug_log("Token validation HTTP error: $httpCode");
        return [
            'valid' => false,
            'error' => get_error_message('invalid_token')
        ];
    }

    $data = json_decode($response, true);

    if (!$data || !isset($data['success'])) {
        debug_log("Token validation invalid response");
        return [
            'valid' => false,
            'error' => get_error_message('api_error')
        ];
    }

    $isValid = $data['success'] === true;
    debug_log("Token validation result", ['valid' => $isValid]);

    return [
        'valid' => $isValid,
        'error' => $isValid ? null : get_error_message('invalid_token'),
        'data' => $data
    ];
}
?>
