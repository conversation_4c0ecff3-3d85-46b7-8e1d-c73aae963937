<?php
function cf_api_request($method, $endpoint, $data = null) {
    if (!isset($_SESSION['token'])) return false;
    $headers = [
        "Authorization: Bearer " . $_SESSION['token'],
        "Content-Type: application/json"
    ];
    $url = "https://api.cloudflare.com/client/v4/$endpoint";
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    $response = curl_exec($ch);
    curl_close($ch);
    return json_decode($response, true);
}
