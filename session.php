<?php
session_start();
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $token = $_POST['token'] ?? '';

    if (!$email || !$token) {
        header("Location: index.php?error=Missing+credentials");
        exit;
    }

    // Test API token by listing zones
    $headers = [
        "Authorization: Bearer $token",
        "Content-Type: application/json"
    ];
    $ch = curl_init("https://api.cloudflare.com/client/v4/zones");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200 && strpos($response, '"success":true') !== false) {
        $_SESSION['email'] = $email;
        $_SESSION['token'] = $token;
        header("Location: dashboard.php");
        exit;
    } else {
        header("Location: index.php?error=Invalid+token");
        exit;
    }
}
