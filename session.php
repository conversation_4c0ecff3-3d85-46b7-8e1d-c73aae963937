<?php
session_start();
require_once 'functions.php';
require_once 'cloudflare.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $token = trim($_POST['token'] ?? '');

    // 验证输入
    if (!$email || !$token) {
        header("Location: index.php?error=" . urlencode("请提供邮箱和 API Token"));
        exit;
    }

    // 验证邮箱格式
    if (!is_valid_email($email)) {
        header("Location: index.php?error=" . urlencode("请提供有效的邮箱地址"));
        exit;
    }

    // 验证 API Token
    $validation = validate_cf_token($token);

    if (!$validation['valid']) {
        $errorMsg = $validation['error'] ?? 'Invalid API token';
        header("Location: index.php?error=" . urlencode($errorMsg));
        exit;
    }

    // 设置会话变量
    $_SESSION['email'] = $email;
    $_SESSION['token'] = $token;
    $_SESSION['login_time'] = time();

    // 记录登录日志
    log_user_action('login', ['email' => $email]);

    // 重定向到控制面板
    header("Location: dashboard.php?success=" . urlencode(get_success_message('login_success')));
    exit;
}
