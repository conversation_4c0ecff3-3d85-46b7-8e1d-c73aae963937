document.addEventListener("DOMContentLoaded", () => {
    console.log("DNS Manager Ready");

    // 全局变量
    let currentZoneId = null;
    let currentZoneName = null;
    let dnsRecords = [];

    // 初始化
    init();

    async function init() {
        try {
            // 检查认证状态
            const authResponse = await fetch('api/auth.php');
            const authData = await authResponse.json();

            if (!authData.authenticated) {
                window.location.href = 'index.php?error=Session+expired';
                return;
            }

            // 加载域名列表
            await loadZones();
        } catch (error) {
            console.error('Initialization error:', error);
            showError('Failed to initialize application');
        }
    }

    async function loadZones() {
        try {
            showLoading('Loading zones...');

            const response = await fetch('api/zones.php');
            const data = await response.json();

            if (data.success) {
                displayZones(data.zones);
            } else {
                showError('Failed to load zones: ' + (data.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error loading zones:', error);
            showError('Network error while loading zones');
        } finally {
            hideLoading();
        }
    }

    function displayZones(zones) {
        const container = document.getElementById('dns-manager');

        if (zones.length === 0) {
            container.innerHTML = '<p>No zones found. Please add a domain to your Cloudflare account.</p>';
            return;
        }

        let html = '<div class="zones-section">';
        html += '<h3>Select a Zone to manage DNS records:</h3>';
        html += '<div class="zones-grid">';

        zones.forEach(zone => {
            html += `
                <div class="zone-card" onclick="selectZone('${zone.id}', '${zone.name}')">
                    <h4>${zone.name}</h4>
                    <p>Status: ${zone.status}</p>
                    <p>Records: ${zone.dns_records_count || 'N/A'}</p>
                </div>
            `;
        });

        html += '</div></div>';
        html += '<div id="records-section" style="display: none;"></div>';

        container.innerHTML = html;
    }

    // 全局函数，供 HTML onclick 调用
    window.selectZone = async function(zoneId, zoneName) {
        currentZoneId = zoneId;
        currentZoneName = zoneName;

        try {
            await loadDnsRecords();
        } catch (error) {
            console.error('Error selecting zone:', error);
            showError('Failed to load DNS records for ' + zoneName);
        }
    };

    async function loadDnsRecords() {
        try {
            showLoading('Loading DNS records...');

            const response = await fetch(`api/records.php?zone_id=${currentZoneId}`);
            const data = await response.json();

            if (data.success) {
                dnsRecords = data.records;
                displayDnsRecords();
            } else {
                showError('Failed to load DNS records: ' + (data.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error loading DNS records:', error);
            showError('Network error while loading DNS records');
        } finally {
            hideLoading();
        }
    }

    function displayDnsRecords() {
        const recordsSection = document.getElementById('records-section');
        recordsSection.style.display = 'block';

        let html = `
            <div class="records-header">
                <h3>DNS Records for ${currentZoneName}</h3>
                <button onclick="showAddRecordForm()" class="btn btn-primary">Add Record</button>
                <button onclick="goBackToZones()" class="btn btn-secondary">Back to Zones</button>
            </div>

            <div id="add-record-form" style="display: none;"></div>

            <div class="records-table">
                <table>
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Name</th>
                            <th>Content</th>
                            <th>TTL</th>
                            <th>Proxied</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        if (dnsRecords.length === 0) {
            html += '<tr><td colspan="6">No DNS records found</td></tr>';
        } else {
            dnsRecords.forEach(record => {
                html += `
                    <tr>
                        <td>${record.type}</td>
                        <td>${record.name}</td>
                        <td>${record.content}</td>
                        <td>${record.ttl === 1 ? 'Auto' : record.ttl}</td>
                        <td>${record.proxied ? 'Yes' : 'No'}</td>
                        <td>
                            <button onclick="editRecord('${record.id}')" class="btn btn-sm">Edit</button>
                            <button onclick="deleteRecord('${record.id}')" class="btn btn-sm btn-danger">Delete</button>
                        </td>
                    </tr>
                `;
            });
        }

        html += `
                    </tbody>
                </table>
            </div>
        `;

        recordsSection.innerHTML = html;
    }

    // 全局函数
    window.goBackToZones = function() {
        document.getElementById('records-section').style.display = 'none';
        currentZoneId = null;
        currentZoneName = null;
    };

    window.showAddRecordForm = function() {
        const formContainer = document.getElementById('add-record-form');
        formContainer.style.display = 'block';
        formContainer.innerHTML = generateRecordForm();
    };

    window.hideAddRecordForm = function() {
        document.getElementById('add-record-form').style.display = 'none';
    };

    function generateRecordForm(record = null) {
        const isEdit = record !== null;
        const title = isEdit ? 'Edit DNS Record' : 'Add DNS Record';

        return `
            <div class="record-form">
                <h4>${title}</h4>
                <form id="dns-record-form" onsubmit="submitRecord(event, ${isEdit ? "'" + record.id + "'" : 'null'})">
                    <div class="form-group">
                        <label>Type:</label>
                        <select name="type" required onchange="handleTypeChange(this.value)">
                            <option value="">Select Type</option>
                            <option value="A" ${record?.type === 'A' ? 'selected' : ''}>A</option>
                            <option value="AAAA" ${record?.type === 'AAAA' ? 'selected' : ''}>AAAA</option>
                            <option value="CNAME" ${record?.type === 'CNAME' ? 'selected' : ''}>CNAME</option>
                            <option value="MX" ${record?.type === 'MX' ? 'selected' : ''}>MX</option>
                            <option value="TXT" ${record?.type === 'TXT' ? 'selected' : ''}>TXT</option>
                            <option value="NS" ${record?.type === 'NS' ? 'selected' : ''}>NS</option>
                            <option value="SRV" ${record?.type === 'SRV' ? 'selected' : ''}>SRV</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Name:</label>
                        <input type="text" name="name" value="${record?.name || ''}" required>
                    </div>

                    <div class="form-group">
                        <label>Content:</label>
                        <input type="text" name="content" value="${record?.content || ''}" required>
                    </div>

                    <div class="form-group" id="priority-group" style="display: ${record?.type === 'MX' ? 'block' : 'none'};">
                        <label>Priority:</label>
                        <input type="number" name="priority" value="${record?.priority || 10}" min="0" max="65535">
                    </div>

                    <div class="form-group">
                        <label>TTL:</label>
                        <select name="ttl">
                            <option value="1" ${record?.ttl === 1 ? 'selected' : ''}>Auto</option>
                            <option value="300" ${record?.ttl === 300 ? 'selected' : ''}>5 minutes</option>
                            <option value="1800" ${record?.ttl === 1800 ? 'selected' : ''}>30 minutes</option>
                            <option value="3600" ${record?.ttl === 3600 ? 'selected' : ''}>1 hour</option>
                            <option value="86400" ${record?.ttl === 86400 ? 'selected' : ''}>1 day</option>
                        </select>
                    </div>

                    <div class="form-group" id="proxied-group">
                        <label>
                            <input type="checkbox" name="proxied" ${record?.proxied ? 'checked' : ''}>
                            Proxied (Orange Cloud)
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">${isEdit ? 'Update' : 'Add'} Record</button>
                        <button type="button" onclick="hideAddRecordForm()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        `;
    }

    // 全局函数
    window.handleTypeChange = function(type) {
        const priorityGroup = document.getElementById('priority-group');
        const proxiedGroup = document.getElementById('proxied-group');

        // MX 记录显示优先级字段
        priorityGroup.style.display = type === 'MX' ? 'block' : 'none';

        // 只有 A 和 AAAA 记录可以代理
        const proxiedCheckbox = proxiedGroup.querySelector('input[name="proxied"]');
        if (type === 'A' || type === 'AAAA') {
            proxiedGroup.style.display = 'block';
        } else {
            proxiedGroup.style.display = 'none';
            proxiedCheckbox.checked = false;
        }
    };

    window.submitRecord = async function(event, recordId) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);

        const data = {
            type: formData.get('type'),
            name: formData.get('name'),
            content: formData.get('content'),
            ttl: parseInt(formData.get('ttl')),
            proxied: formData.get('proxied') === 'on'
        };

        // MX 记录添加优先级
        if (data.type === 'MX') {
            data.priority = parseInt(formData.get('priority')) || 10;
        }

        try {
            showLoading(recordId ? 'Updating record...' : 'Adding record...');

            const url = recordId
                ? `api/records.php?zone_id=${currentZoneId}&record_id=${recordId}`
                : `api/records.php?zone_id=${currentZoneId}`;

            const method = recordId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                showSuccess(recordId ? 'Record updated successfully' : 'Record added successfully');
                hideAddRecordForm();
                await loadDnsRecords(); // 重新加载记录列表
            } else {
                showError('Failed to save record: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error saving record:', error);
            showError('Network error while saving record');
        } finally {
            hideLoading();
        }
    };

    window.editRecord = function(recordId) {
        const record = dnsRecords.find(r => r.id === recordId);
        if (!record) {
            showError('Record not found');
            return;
        }

        const formContainer = document.getElementById('add-record-form');
        formContainer.style.display = 'block';
        formContainer.innerHTML = generateRecordForm(record);

        // 触发类型变化事件以显示/隐藏相关字段
        handleTypeChange(record.type);
    };

    window.deleteRecord = async function(recordId) {
        const record = dnsRecords.find(r => r.id === recordId);
        if (!record) {
            showError('Record not found');
            return;
        }

        if (!confirm(`Are you sure you want to delete the ${record.type} record for ${record.name}?`)) {
            return;
        }

        try {
            showLoading('Deleting record...');

            const response = await fetch(`api/records.php?zone_id=${currentZoneId}&record_id=${recordId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                showSuccess('Record deleted successfully');
                await loadDnsRecords(); // 重新加载记录列表
            } else {
                showError('Failed to delete record: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error deleting record:', error);
            showError('Network error while deleting record');
        } finally {
            hideLoading();
        }
    };

    // 工具函数
    function showLoading(message = 'Loading...') {
        // 创建或更新加载指示器
        let loader = document.getElementById('loading-indicator');
        if (!loader) {
            loader = document.createElement('div');
            loader.id = 'loading-indicator';
            loader.className = 'loading-indicator';
            document.body.appendChild(loader);
        }
        loader.innerHTML = `<div class="loading-content">${message}</div>`;
        loader.style.display = 'flex';
    }

    function hideLoading() {
        const loader = document.getElementById('loading-indicator');
        if (loader) {
            loader.style.display = 'none';
        }
    }

    function showError(message) {
        showNotification(message, 'error');
    }

    function showSuccess(message) {
        showNotification(message, 'success');
    }

    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
});
