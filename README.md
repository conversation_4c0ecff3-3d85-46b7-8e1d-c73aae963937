# Cloudflare DNS UI

一个基于 PHP 的 Cloudflare DNS 管理界面，提供简洁易用的 Web 界面来管理您的 Cloudflare DNS 记录。

## 🌟 功能特性

- 🔐 **安全登录**: 使用 Cloudflare API Token 进行身份验证
- 🌐 **域名管理**: 自动获取并显示所有 Cloudflare 域名
- 📝 **DNS 记录管理**: 支持添加、编辑、删除各种类型的 DNS 记录
- 📱 **响应式设计**: 完美支持桌面和移动设备
- 🎨 **现代化界面**: 简洁美观的用户界面
- ⚡ **实时更新**: AJAX 技术实现无刷新操作

## 🚀 支持的 DNS 记录类型

- A 记录 (IPv4 地址)
- AAAA 记录 (IPv6 地址)
- CNAME 记录 (别名)
- MX 记录 (邮件交换，支持优先级设置)
- TXT 记录 (文本记录)
- NS 记录 (名称服务器)
- SRV 记录 (服务记录)

## 📋 系统要求

- PHP 7.4 或更高版本
- cURL 扩展
- JSON 扩展
- Web 服务器 (Apache/Nginx)
- 有效的 Cloudflare 账户和 API Token

## 🛠️ 安装步骤

1. **下载代码**
   ```bash
   git clone <repository-url>
   cd cloudflare-dns-ui
   ```

2. **配置 Web 服务器**
   - 将项目文件放置在 Web 服务器的文档根目录
   - 确保 PHP 和必要扩展已安装

3. **测试环境**
   - 访问 `test.php` 检查系统环境是否满足要求
   - 确保所有测试项目都显示为 ✅

4. **获取 Cloudflare API Token**
   - 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - 访问 [API Tokens 页面](https://dash.cloudflare.com/profile/api-tokens)
   - 点击 "Create Token"
   - 选择 "Zone:Zone:Read" 和 "Zone:DNS:Edit" 权限
   - 复制生成的 Token

5. **开始使用**
   - 访问 `index.php` 登录页面
   - 输入您的邮箱和 API Token
   - 开始管理您的 DNS 记录

## 📁 文件结构

```
cloudflare-dns-ui/
├── docs/
│   └── design.md          # 详细设计文档
├── assets/
│   ├── style.css          # 样式文件
│   └── script.js          # 前端 JavaScript
├── api/
│   ├── zones.php          # 域名管理 API
│   ├── records.php        # DNS 记录管理 API
│   └── auth.php           # 认证 API
├── index.php              # 登录页面
├── dashboard.php          # 主控制面板
├── session.php            # 登录处理
├── logout.php             # 登出处理
├── functions.php          # 通用函数
├── cloudflare.php         # Cloudflare API 封装
├── test.php               # 环境测试脚本
└── README.md              # 说明文档
```

## 🔧 使用说明

### 登录
1. 在登录页面输入您的 Cloudflare 账户邮箱
2. 输入您的 API Token
3. 点击登录按钮

### 管理 DNS 记录
1. 登录后会显示您的所有域名
2. 点击要管理的域名
3. 查看该域名下的所有 DNS 记录
4. 使用 "Add Record" 按钮添加新记录
5. 使用 "Edit" 按钮修改现有记录
6. 使用 "Delete" 按钮删除记录

### 记录类型说明
- **A 记录**: 将域名指向 IPv4 地址
- **AAAA 记录**: 将域名指向 IPv6 地址
- **CNAME 记录**: 创建域名别名
- **MX 记录**: 设置邮件服务器（需要设置优先级）
- **TXT 记录**: 存储文本信息（常用于验证）
- **NS 记录**: 设置名称服务器
- **SRV 记录**: 设置服务记录

## 🛡️ 安全特性

- API Token 安全存储在服务器端会话中
- 所有用户输入都经过清理和验证
- 会话自动过期（24小时）
- CSRF 保护和输入验证
- 安全的会话销毁机制

## 🐛 故障排除

### 常见问题

1. **"Call to undefined function require_login()" 错误**
   - 已修复：确保 `functions.php` 被正确包含

2. **API Token 验证失败**
   - 检查 Token 是否有正确的权限
   - 确认 Token 没有过期
   - 验证网络连接到 Cloudflare API

3. **页面样式不显示**
   - 检查 `assets/` 目录权限
   - 确认 Web 服务器可以访问静态文件

4. **会话问题**
   - 检查 PHP 会话配置
   - 确认会话存储目录权限

### 调试步骤
1. 访问 `test.php` 检查环境
2. 检查 PHP 错误日志
3. 使用浏览器开发者工具检查网络请求
4. 验证 Cloudflare API Token 权限

## 📝 更新日志

### v1.1.0 (最新)
- 🐛 修复 `require_login()` 函数未定义错误
- ✨ 完善 DNS 记录管理功能
- 🎨 改进用户界面设计
- 🔒 增强安全性和错误处理
- 📱 添加响应式设计支持
- 🌐 界面中文本地化

### v1.0.0
- 🎉 初始版本发布
- 🔐 基础登录功能
- 🌐 域名列表显示
- 📝 基础 DNS 记录管理

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📞 支持

如果您遇到问题或需要帮助，请：
1. 查看本 README 文档
2. 检查 `docs/design.md` 详细设计文档
3. 运行 `test.php` 检查环境
4. 提交 Issue 描述问题
