
/* 基础样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    color: #333;
}

/* 登录页面样式 */
.login-container {
    width: 400px;
    margin: 80px auto;
    padding: 30px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.login-container h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
}

/* 主控制面板样式 */
.dashboard-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e1e8ed;
}

.dashboard-header h2 {
    margin: 0;
    color: #2c3e50;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info span {
    color: #7f8c8d;
    font-weight: 500;
}

.loading-placeholder {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

/* 表单样式 */
label {
    display: block;
    margin-top: 15px;
    font-weight: 600;
    color: #555;
}

input, select, textarea {
    width: 100%;
    padding: 12px;
    margin-top: 5px;
    border: 2px solid #e1e8ed;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #3498db;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    margin: 5px;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

button[type="submit"] {
    margin-top: 20px;
    width: 100%;
}

.logout-btn {
    float: right;
    color: #e74c3c;
    text-decoration: none;
    font-weight: 600;
}

.logout-btn:hover {
    color: #c0392b;
}

/* 域名网格样式 */
.zones-section {
    margin-bottom: 30px;
}

.zones-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.zones-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.zone-card {
    background: #fff;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.zone-card:hover {
    border-color: #3498db;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
}

.zone-card h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 18px;
}

.zone-card p {
    margin: 5px 0;
    color: #7f8c8d;
    font-size: 14px;
}

/* DNS 记录部分样式 */
.records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e1e8ed;
}

.records-header h3 {
    margin: 0;
    color: #2c3e50;
}

/* 记录表格样式 */
.records-table {
    overflow-x: auto;
    margin-top: 20px;
}

.records-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.records-table th,
.records-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e1e8ed;
}

.records-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
}

.records-table tr:hover {
    background: #f8f9fa;
}

.records-table tr:last-child td {
    border-bottom: none;
}

/* 记录表单样式 */
.record-form {
    background: #f8f9fa;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    padding: 25px;
    margin: 20px 0;
}

.record-form h4 {
    margin: 0 0 20px 0;
    color: #2c3e50;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    margin-top: 0;
    margin-bottom: 5px;
}

.form-actions {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e1e8ed;
}

.form-actions .btn {
    margin-right: 10px;
}

/* 复选框样式 */
input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
    transform: scale(1.2);
}

/* 加载指示器 */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    color: #2c3e50;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
}

.loading-content::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid #3498db;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 350px;
    word-wrap: break-word;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: #27ae60;
}

.notification-error {
    background: #e74c3c;
}

.notification-info {
    background: #3498db;
}

/* 错误和成功消息 */
.error {
    color: #e74c3c;
    background: #fdf2f2;
    padding: 10px;
    border-radius: 6px;
    border-left: 4px solid #e74c3c;
    margin: 15px 0;
}

.success {
    color: #27ae60;
    background: #f0f9f4;
    padding: 10px;
    border-radius: 6px;
    border-left: 4px solid #27ae60;
    margin: 15px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-container {
        margin: 10px;
        padding: 15px;
    }

    .zones-grid {
        grid-template-columns: 1fr;
    }

    .records-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .records-table {
        font-size: 14px;
    }

    .records-table th,
    .records-table td {
        padding: 8px 10px;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }
}
