<?php
session_start();
require_once 'functions.php';

// 记录登出日志
if (isset($_SESSION['email'])) {
    log_user_action('logout', ['email' => $_SESSION['email']]);
}

session_unset();
session_destroy();

// 清除会话 cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

header("Location: index.php?success=" . urlencode(get_success_message('logout_success')));
exit;
