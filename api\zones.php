<?php
session_start();
require_once '../functions.php';
require_once '../cloudflare.php';

header('Content-Type: application/json');

// 检查登录状态
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// 获取所有 Zone
$response = cf_api_request('GET', 'zones');

if ($response && isset($response['success']) && $response['success']) {
    echo json_encode([
        'success' => true,
        'zones' => $response['result']
    ]);
} else {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch zones',
        'details' => $response['errors'] ?? []
    ]);
}
?>
