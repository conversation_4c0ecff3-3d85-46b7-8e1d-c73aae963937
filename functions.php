<?php
require_once 'config.php';

/**
 * 清理用户输入，防止 XSS 攻击
 *
 * @param string $value 要清理的值
 * @return string 清理后的值
 */
function sanitize($value) {
    return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
}

/**
 * 检查用户是否已登录，未登录则重定向到登录页
 */
function require_login() {
    if (!is_logged_in()) {
        header("Location: index.php?error=" . urlencode(get_error_message('session_expired')));
        exit;
    }

    // 检查会话是否过期
    if (is_session_expired()) {
        session_unset();
        session_destroy();
        header("Location: index.php?error=" . urlencode(get_error_message('session_expired')));
        exit;
    }
}

/**
 * 检查用户是否已登录
 *
 * @return bool
 */
function is_logged_in() {
    return isset($_SESSION['email']) && isset($_SESSION['token']) && isset($_SESSION['login_time']);
}

/**
 * 检查会话是否过期
 *
 * @return bool
 */
function is_session_expired() {
    if (!isset($_SESSION['login_time'])) {
        return true;
    }

    $sessionTimeout = get_config('SESSION_TIMEOUT', 86400);
    return (time() - $_SESSION['login_time']) > $sessionTimeout;
}

/**
 * 生成 CSRF Token
 *
 * @return string
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * 验证 CSRF Token
 *
 * @param string $token 要验证的 token
 * @return bool
 */
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * 格式化文件大小
 *
 * @param int $bytes 字节数
 * @return string 格式化后的大小
 */
function format_bytes($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= (1 << (10 * $pow));

    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * 验证邮箱地址格式
 *
 * @param string $email 邮箱地址
 * @return bool
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * 验证域名格式
 *
 * @param string $domain 域名
 * @return bool
 */
function is_valid_domain($domain) {
    return filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) !== false;
}

/**
 * 验证 IP 地址格式
 *
 * @param string $ip IP 地址
 * @param int $flags 验证标志 (FILTER_FLAG_IPV4 或 FILTER_FLAG_IPV6)
 * @return bool
 */
function is_valid_ip($ip, $flags = null) {
    return filter_var($ip, FILTER_VALIDATE_IP, $flags) !== false;
}

/**
 * 记录用户操作日志
 *
 * @param string $action 操作类型
 * @param array $details 操作详情
 */
function log_user_action($action, $details = []) {
    if (get_config('LOG_API_REQUESTS', false)) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'user' => $_SESSION['email'] ?? 'anonymous',
            'action' => $action,
            'details' => $details,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        error_log('USER_ACTION: ' . json_encode($logData));
    }
}
