<?php
/**
 * 简单的测试脚本，用于验证核心功能
 */

// 测试基础 PHP 功能
echo "<h2>PHP 环境测试</h2>\n";
echo "PHP 版本: " . PHP_VERSION . "<br>\n";
echo "cURL 扩展: " . (extension_loaded('curl') ? '✅ 已安装' : '❌ 未安装') . "<br>\n";
echo "JSON 扩展: " . (extension_loaded('json') ? '✅ 已安装' : '❌ 未安装') . "<br>\n";
echo "会话支持: " . (function_exists('session_start') ? '✅ 支持' : '❌ 不支持') . "<br>\n";

// 测试文件包含
echo "<h2>文件包含测试</h2>\n";
try {
    require_once 'config.php';
    echo "config.php: ✅ 加载成功<br>\n";

    // 测试配置常量
    echo "APP_NAME: " . (defined('APP_NAME') ? '✅ ' . APP_NAME : '❌ 未定义') . "<br>\n";
    echo "APP_VERSION: " . (defined('APP_VERSION') ? '✅ ' . APP_VERSION : '❌ 未定义') . "<br>\n";
    echo "SESSION_TIMEOUT: " . (defined('SESSION_TIMEOUT') ? '✅ ' . SESSION_TIMEOUT . '秒' : '❌ 未定义') . "<br>\n";
} catch (Exception $e) {
    echo "config.php: ❌ 加载失败 - " . $e->getMessage() . "<br>\n";
}

try {
    require_once 'functions.php';
    echo "functions.php: ✅ 加载成功<br>\n";

    // 测试函数是否存在
    echo "require_login() 函数: " . (function_exists('require_login') ? '✅ 存在' : '❌ 不存在') . "<br>\n";
    echo "is_logged_in() 函数: " . (function_exists('is_logged_in') ? '✅ 存在' : '❌ 不存在') . "<br>\n";
    echo "sanitize() 函数: " . (function_exists('sanitize') ? '✅ 存在' : '❌ 不存在') . "<br>\n";
    echo "is_valid_email() 函数: " . (function_exists('is_valid_email') ? '✅ 存在' : '❌ 不存在') . "<br>\n";
    echo "generate_csrf_token() 函数: " . (function_exists('generate_csrf_token') ? '✅ 存在' : '❌ 不存在') . "<br>\n";
} catch (Exception $e) {
    echo "functions.php: ❌ 加载失败 - " . $e->getMessage() . "<br>\n";
}

try {
    require_once 'cloudflare.php';
    echo "cloudflare.php: ✅ 加载成功<br>\n";
    
    // 测试函数是否存在
    echo "cf_api_request() 函数: " . (function_exists('cf_api_request') ? '✅ 存在' : '❌ 不存在') . "<br>\n";
    echo "validate_cf_token() 函数: " . (function_exists('validate_cf_token') ? '✅ 存在' : '❌ 不存在') . "<br>\n";
} catch (Exception $e) {
    echo "cloudflare.php: ❌ 加载失败 - " . $e->getMessage() . "<br>\n";
}

// 测试文件权限
echo "<h2>文件权限测试</h2>\n";
$files = ['index.php', 'dashboard.php', 'session.php', 'logout.php', 'assets/style.css', 'assets/script.js'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "$file: ✅ 存在且可读<br>\n";
    } else {
        echo "$file: ❌ 不存在或不可读<br>\n";
    }
}

// 测试 API 目录
echo "<h2>API 目录测试</h2>\n";
$apiFiles = ['api/zones.php', 'api/records.php', 'api/auth.php'];
foreach ($apiFiles as $file) {
    if (file_exists($file)) {
        echo "$file: ✅ 存在且可读<br>\n";
    } else {
        echo "$file: ❌ 不存在或不可读<br>\n";
    }
}

// 测试网络连接
echo "<h2>网络连接测试</h2>\n";
if (function_exists('curl_init')) {
    $ch = curl_init('https://api.cloudflare.com/client/v4/');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "Cloudflare API 连接: ❌ 失败 - $error<br>\n";
    } elseif ($httpCode == 400) {
        echo "Cloudflare API 连接: ✅ 成功 (HTTP $httpCode - 正常，因为没有提供认证)<br>\n";
    } else {
        echo "Cloudflare API 连接: ⚠️ HTTP $httpCode<br>\n";
    }
} else {
    echo "Cloudflare API 连接: ❌ cURL 不可用<br>\n";
}

echo "<h2>测试完成</h2>\n";
echo "<p>如果所有测试都通过，您可以访问 <a href='index.php'>登录页面</a> 开始使用。</p>\n";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
br { line-height: 1.6; }
</style>
