<?php
/**
 * Cloudflare DNS UI 配置文件
 */

// 应用配置
define('APP_NAME', 'Cloudflare DNS UI');
define('APP_VERSION', '1.1.0');

// 会话配置
define('SESSION_TIMEOUT', 86400); // 24小时 (秒)
define('SESSION_NAME', 'cloudflare_dns_ui');

// API 配置
define('CLOUDFLARE_API_BASE', 'https://api.cloudflare.com/client/v4/');
define('API_TIMEOUT', 30); // API 请求超时时间 (秒)
define('API_CONNECT_TIMEOUT', 10); // 连接超时时间 (秒)

// 安全配置
define('CSRF_TOKEN_NAME', '_token');
define('MAX_LOGIN_ATTEMPTS', 5); // 最大登录尝试次数
define('LOGIN_LOCKOUT_TIME', 900); // 登录锁定时间 (15分钟)

// 调试配置
define('DEBUG_MODE', false); // 生产环境请设置为 false
define('LOG_API_REQUESTS', false); // 是否记录 API 请求日志

// 支持的 DNS 记录类型
define('SUPPORTED_DNS_TYPES', [
    'A' => 'A (IPv4 地址)',
    'AAAA' => 'AAAA (IPv6 地址)',
    'CNAME' => 'CNAME (别名)',
    'MX' => 'MX (邮件交换)',
    'TXT' => 'TXT (文本记录)',
    'NS' => 'NS (名称服务器)',
    'SRV' => 'SRV (服务记录)',
    'CAA' => 'CAA (证书颁发机构授权)',
    'PTR' => 'PTR (反向 DNS)'
]);

// TTL 选项
define('TTL_OPTIONS', [
    1 => '自动',
    300 => '5 分钟',
    1800 => '30 分钟',
    3600 => '1 小时',
    7200 => '2 小时',
    18000 => '5 小时',
    43200 => '12 小时',
    86400 => '1 天'
]);

// 错误消息
define('ERROR_MESSAGES', [
    'invalid_token' => 'API Token 无效或已过期',
    'network_error' => '网络连接错误，请稍后重试',
    'session_expired' => '会话已过期，请重新登录',
    'permission_denied' => '权限不足，无法执行此操作',
    'invalid_input' => '输入数据无效',
    'api_error' => 'API 请求失败',
    'unknown_error' => '发生未知错误'
]);

// 成功消息
define('SUCCESS_MESSAGES', [
    'login_success' => '登录成功',
    'logout_success' => '已成功退出登录',
    'record_added' => 'DNS 记录添加成功',
    'record_updated' => 'DNS 记录更新成功',
    'record_deleted' => 'DNS 记录删除成功'
]);

/**
 * 获取配置值
 * 
 * @param string $key 配置键名
 * @param mixed $default 默认值
 * @return mixed 配置值
 */
function get_config($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

/**
 * 检查是否为调试模式
 * 
 * @return bool
 */
function is_debug_mode() {
    return get_config('DEBUG_MODE', false);
}

/**
 * 获取错误消息
 * 
 * @param string $key 错误键名
 * @return string 错误消息
 */
function get_error_message($key) {
    $messages = get_config('ERROR_MESSAGES', []);
    return $messages[$key] ?? $messages['unknown_error'] ?? '发生错误';
}

/**
 * 获取成功消息
 * 
 * @param string $key 成功消息键名
 * @return string 成功消息
 */
function get_success_message($key) {
    $messages = get_config('SUCCESS_MESSAGES', []);
    return $messages[$key] ?? '操作成功';
}

/**
 * 记录调试信息
 * 
 * @param string $message 消息
 * @param array $context 上下文数据
 */
function debug_log($message, $context = []) {
    if (is_debug_mode()) {
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = empty($context) ? '' : ' ' . json_encode($context);
        error_log("[$timestamp] DEBUG: $message$contextStr");
    }
}
?>
