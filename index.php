<?php
session_start();
if (isset($_SESSION['email']) && isset($_SESSION['token'])) {
    header("Location: dashboard.php");
    exit;
}
$error = $_GET['error'] ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Cloudflare DNS Manager - Login</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="login-container">
        <h2>Login to Cloudflare DNS Manager</h2>
        <?php if ($error): ?>
            <p class="error"><?= htmlspecialchars($error) ?></p>
        <?php endif; ?>
        <form method="post" action="session.php">
            <label>Email:</label>
            <input type="email" name="email" required>
            <label>API Token:</label>
            <input type="password" name="token" required>
            <button type="submit">Login</button>
        </form>
    </div>
</body>
</html>
