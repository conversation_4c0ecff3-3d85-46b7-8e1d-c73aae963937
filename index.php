<?php
session_start();
if (isset($_SESSION['email']) && isset($_SESSION['token'])) {
    header("Location: dashboard.php");
    exit;
}
$error = $_GET['error'] ?? '';
$success = $_GET['success'] ?? '';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare DNS 管理器 - 登录</title>
    <link rel="stylesheet" href="assets/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌐</text></svg>">
</head>
<body>
    <div class="login-container">
        <h2>🌐 Cloudflare DNS 管理器</h2>
        <p style="text-align: center; color: #7f8c8d; margin-bottom: 25px;">
            使用您的 Cloudflare 账户登录
        </p>

        <?php if ($error): ?>
            <div class="error"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="success"><?= htmlspecialchars($success) ?></div>
        <?php endif; ?>

        <form method="post" action="session.php" id="login-form">
            <label for="email">邮箱地址:</label>
            <input type="email" id="email" name="email" required
                   placeholder="<EMAIL>"
                   value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">

            <label for="token">API Token:</label>
            <input type="password" id="token" name="token" required
                   placeholder="您的 Cloudflare API Token">

            <button type="submit" id="login-btn">登录</button>
        </form>

        <div style="margin-top: 25px; padding-top: 20px; border-top: 1px solid #e1e8ed;">
            <h4 style="color: #2c3e50; margin-bottom: 10px;">如何获取 API Token:</h4>
            <ol style="color: #7f8c8d; font-size: 14px; line-height: 1.6;">
                <li>访问 <a href="https://dash.cloudflare.com/profile/api-tokens" target="_blank" style="color: #3498db;">Cloudflare API Tokens 页面</a></li>
                <li>点击 "Create Token"</li>
                <li>选择 "Zone:Zone:Read, Zone:DNS:Edit" 权限</li>
                <li>复制生成的 Token 并粘贴到上方</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('login-form').addEventListener('submit', function(e) {
            const btn = document.getElementById('login-btn');
            btn.textContent = '登录中...';
            btn.disabled = true;
        });
    </script>
</body>
</html>
