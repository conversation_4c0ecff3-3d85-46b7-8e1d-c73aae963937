<?php
session_start();
require_once '../functions.php';
require_once '../cloudflare.php';

header('Content-Type: application/json');

// 检查登录状态
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$zone_id = $_GET['zone_id'] ?? '';

if (!$zone_id) {
    http_response_code(400);
    echo json_encode(['error' => 'Zone ID is required']);
    exit;
}

switch ($method) {
    case 'GET':
        // 获取 DNS 记录
        $response = cf_api_request('GET', "zones/$zone_id/dns_records");
        
        if ($response && isset($response['success']) && $response['success']) {
            echo json_encode([
                'success' => true,
                'records' => $response['result']
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to fetch DNS records',
                'details' => $response['errors'] ?? []
            ]);
        }
        break;
        
    case 'POST':
        // 添加 DNS 记录
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['type']) || !isset($input['name']) || !isset($input['content'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing required fields: type, name, content']);
            exit;
        }
        
        $data = [
            'type' => sanitize($input['type']),
            'name' => sanitize($input['name']),
            'content' => sanitize($input['content']),
            'ttl' => isset($input['ttl']) ? (int)$input['ttl'] : 1,
            'proxied' => isset($input['proxied']) ? (bool)$input['proxied'] : false
        ];
        
        // MX 记录需要 priority
        if ($data['type'] === 'MX' && isset($input['priority'])) {
            $data['priority'] = (int)$input['priority'];
        }
        
        $response = cf_api_request('POST', "zones/$zone_id/dns_records", $data);
        
        if ($response && isset($response['success']) && $response['success']) {
            echo json_encode([
                'success' => true,
                'record' => $response['result']
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to create DNS record',
                'details' => $response['errors'] ?? []
            ]);
        }
        break;
        
    case 'PUT':
        // 更新 DNS 记录
        $record_id = $_GET['record_id'] ?? '';
        if (!$record_id) {
            http_response_code(400);
            echo json_encode(['error' => 'Record ID is required']);
            exit;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON input']);
            exit;
        }
        
        $data = [];
        if (isset($input['type'])) $data['type'] = sanitize($input['type']);
        if (isset($input['name'])) $data['name'] = sanitize($input['name']);
        if (isset($input['content'])) $data['content'] = sanitize($input['content']);
        if (isset($input['ttl'])) $data['ttl'] = (int)$input['ttl'];
        if (isset($input['proxied'])) $data['proxied'] = (bool)$input['proxied'];
        if (isset($input['priority'])) $data['priority'] = (int)$input['priority'];
        
        $response = cf_api_request('PUT', "zones/$zone_id/dns_records/$record_id", $data);
        
        if ($response && isset($response['success']) && $response['success']) {
            echo json_encode([
                'success' => true,
                'record' => $response['result']
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to update DNS record',
                'details' => $response['errors'] ?? []
            ]);
        }
        break;
        
    case 'DELETE':
        // 删除 DNS 记录
        $record_id = $_GET['record_id'] ?? '';
        if (!$record_id) {
            http_response_code(400);
            echo json_encode(['error' => 'Record ID is required']);
            exit;
        }
        
        $response = cf_api_request('DELETE', "zones/$zone_id/dns_records/$record_id");
        
        if ($response && isset($response['success']) && $response['success']) {
            echo json_encode([
                'success' => true,
                'message' => 'DNS record deleted successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to delete DNS record',
                'details' => $response['errors'] ?? []
            ]);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}
?>
