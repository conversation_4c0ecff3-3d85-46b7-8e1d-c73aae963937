<?php
require_once 'session.php';
require_once 'functions.php';
require_login();

$success = $_GET['success'] ?? '';
$error = $_GET['error'] ?? '';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare DNS 管理面板</title>
    <link rel="stylesheet" href="assets/style.css">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌐</text></svg>">
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h2>🌐 Cloudflare DNS 管理面板</h2>
            <div class="user-info">
                <span>欢迎, <?= htmlspecialchars($_SESSION['email']) ?></span>
                <a href="logout.php" class="logout-btn" onclick="return confirm('确定要退出登录吗？')">退出登录</a>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="success"><?= htmlspecialchars($success) ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="error"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <div id="dns-manager">
            <div class="loading-placeholder">
                <p>正在加载域名列表...</p>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="loading-content">加载中...</div>
    </div>

    <script src="assets/script.js"></script>
</body>
</html>
