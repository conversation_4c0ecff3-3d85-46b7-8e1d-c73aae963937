<?php
session_start();
require_once '../functions.php';

header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // 检查认证状态
        echo json_encode([
            'authenticated' => is_logged_in(),
            'email' => $_SESSION['email'] ?? null
        ]);
        break;
        
    case 'DELETE':
        // 登出
        session_unset();
        session_destroy();
        echo json_encode([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}
?>
