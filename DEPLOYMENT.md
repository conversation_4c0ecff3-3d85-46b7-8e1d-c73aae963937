# 部署检查清单

## 🚀 部署前检查

### 1. 环境要求
- [ ] PHP 7.4 或更高版本
- [ ] cURL 扩展已启用
- [ ] JSON 扩展已启用
- [ ] 会话支持已启用
- [ ] Web 服务器 (Apache/Nginx) 已配置

### 2. 文件权限
- [ ] 所有 PHP 文件可读 (644)
- [ ] 目录权限正确 (755)
- [ ] `assets/` 目录可访问
- [ ] `api/` 目录可访问

### 3. 配置检查
- [ ] `config.php` 中的设置适合生产环境
- [ ] 调试模式已关闭 (`DEBUG_MODE = false`)
- [ ] 会话超时时间合理
- [ ] API 超时设置合适

### 4. 安全设置
- [ ] HTTPS 已启用（生产环境强烈推荐）
- [ ] PHP 错误显示已关闭
- [ ] 敏感文件不可直接访问
- [ ] 会话安全配置已优化

## 🧪 部署后测试

### 1. 基础功能测试
- [ ] 访问 `test.php` 确认所有检查通过
- [ ] 登录页面正常显示
- [ ] 样式和脚本正确加载
- [ ] API 端点可访问

### 2. 登录流程测试
- [ ] 使用有效的 Cloudflare API Token 登录
- [ ] 登录成功后跳转到控制面板
- [ ] 用户信息正确显示
- [ ] 登出功能正常

### 3. DNS 管理测试
- [ ] 域名列表正确加载
- [ ] 选择域名后 DNS 记录正确显示
- [ ] 添加新 DNS 记录功能正常
- [ ] 编辑现有记录功能正常
- [ ] 删除记录功能正常（谨慎测试）

### 4. 错误处理测试
- [ ] 无效 API Token 时显示正确错误
- [ ] 网络错误时有适当提示
- [ ] 表单验证正常工作
- [ ] 会话过期时正确处理

## 🔧 常见问题解决

### 问题：页面显示空白
**解决方案：**
1. 检查 PHP 错误日志
2. 确认所有必需文件存在
3. 验证文件权限
4. 检查 PHP 语法错误

### 问题：样式不显示
**解决方案：**
1. 检查 `assets/` 目录权限
2. 确认 Web 服务器配置允许访问静态文件
3. 检查浏览器控制台错误

### 问题：API 请求失败
**解决方案：**
1. 检查服务器网络连接
2. 验证 cURL 扩展正常工作
3. 确认 Cloudflare API 可访问
4. 检查 API Token 权限

### 问题：会话问题
**解决方案：**
1. 检查 PHP 会话配置
2. 确认会话存储目录权限
3. 验证会话 cookie 设置

## 📊 性能优化建议

### 1. 缓存设置
- 为静态资源设置适当的缓存头
- 考虑使用 CDN 加速静态文件
- 启用 gzip 压缩

### 2. 安全加固
- 设置适当的 HTTP 安全头
- 限制文件上传（如果有）
- 定期更新 PHP 版本

### 3. 监控设置
- 设置错误日志监控
- 配置性能监控
- 设置磁盘空间监控

## 🔄 维护建议

### 定期检查
- [ ] 检查 PHP 错误日志
- [ ] 验证 API Token 有效性
- [ ] 检查会话存储空间
- [ ] 更新依赖和 PHP 版本

### 备份策略
- [ ] 定期备份配置文件
- [ ] 备份自定义修改
- [ ] 文档化所有配置更改

### 更新流程
1. 在测试环境验证更新
2. 备份当前版本
3. 部署新版本
4. 验证功能正常
5. 监控错误日志

## 📞 支持资源

- **项目文档**: `README.md`
- **设计文档**: `docs/design.md`
- **测试工具**: `test.php`
- **Cloudflare API 文档**: https://developers.cloudflare.com/api/

---

**注意**: 在生产环境部署前，请务必在测试环境中完整验证所有功能！
